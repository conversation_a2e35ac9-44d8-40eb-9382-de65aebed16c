package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.ImageUtils;
import com.logic.code.model.vo.ImageInfoVO;
import com.logic.code.service.ImageCompressionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片服务控制器
 * 提供智能图片读取、压缩、缓存等功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/images")
@Slf4j
public class ImageController {

    @Autowired
    private ImageCompressionService imageCompressionService;

    @Autowired
    private ImageUtils imageUtils;

    @Value("${file.upload.path:/uploads}")
    private String uploadPath;

    @Value("${app.image.cache.enabled:true}")
    private boolean cacheEnabled;

    /**
     * 智能图片服务接口
     * 根据请求参数自动选择最优的图片尺寸和质量
     * 
     * @param size 图片尺寸类型 (thumb, medium, large, avatar, banner, comment)
     * @param imagePath 图片路径（通过请求参数传递）
     * @param request HTTP请求对象
     * @return 优化后的图片URL
     */
    @GetMapping("/optimize/{size}")
    public Result<String> getOptimizedImageUrl(
            @PathVariable String size,
            @RequestParam String imagePath,
            HttpServletRequest request) {

        try {
            if (!imagePath.startsWith("/")) {
                imagePath = "/" + imagePath;
            }
            
            // 检查图片是否存在
            Path fullImagePath = Paths.get(uploadPath + imagePath);
            if (!Files.exists(fullImagePath)) {
                log.warn("图片文件不存在: {}", imagePath);
                return Result.failure("图片文件不存在");
            }

            // 获取客户端信息
            String userAgent = request.getHeader("User-Agent");
            String networkType = request.getHeader("X-Network-Type");

            // 智能压缩图片URL
            String optimizedUrl = imageCompressionService.smartCompress(
                imagePath, size, detectDeviceType(userAgent), networkType);

            return Result.success(optimizedUrl);

        } catch (Exception e) {
            log.error("获取优化图片URL时发生错误: {}", e.getMessage(), e);
            return Result.failure("获取优化图片URL失败: " + e.getMessage());
        }
    }

    /**
     * 直接获取优化后的图片资源
     * 
     * @param size 图片尺寸类型
     * @param imagePath 图片路径
     * @param request HTTP请求对象
     * @return 图片资源
     */
    @GetMapping("/serve/{size}")
    public ResponseEntity<Resource> serveOptimizedImage(
            @PathVariable String size,
            @RequestParam String imagePath,
            HttpServletRequest request) {

        try {
            if (!imagePath.startsWith("/")) {
                imagePath = "/" + imagePath;
            }
            
            // 检查图片是否存在
            Path fullImagePath = Paths.get(uploadPath + imagePath);
            if (!Files.exists(fullImagePath)) {
                log.warn("图片文件不存在: {}", imagePath);
                return ResponseEntity.notFound().build();
            }

            // 获取客户端信息
            String userAgent = request.getHeader("User-Agent");
            String acceptHeader = request.getHeader("Accept");
            String networkType = request.getHeader("X-Network-Type");

            // 智能压缩图片URL
            String optimizedUrl = imageCompressionService.smartCompress(
                imagePath, size, detectDeviceType(userAgent), networkType);

            // 检查是否支持WebP格式
            boolean supportsWebP = acceptHeader != null && acceptHeader.contains("image/webp");
            
            // 如果支持WebP，尝试获取WebP版本
            if (supportsWebP) {
                optimizedUrl = tryGetWebPVersion(optimizedUrl);
            }

            // 创建资源对象
            Resource resource;
            try {
                resource = new UrlResource(optimizedUrl);
                if (!resource.exists() || !resource.isReadable()) {
                    // 如果优化版本不存在，返回原始图片
                    resource = new UrlResource("file:" + fullImagePath.toString());
                }
            } catch (MalformedURLException e) {
                // 如果URL格式错误，返回原始图片
                resource = new UrlResource("file:" + fullImagePath.toString());
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(getMediaType(imagePath));
            headers.setCacheControl("public, max-age=86400"); // 缓存24小时
            headers.set("X-Image-Size", size);
            headers.set("X-Optimized", "true");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            log.error("获取图片资源时发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取图片信息接口
     * 返回图片的元数据信息
     * 
     * @param url 图片URL
     * @return 图片信息
     */
    @GetMapping("/info")
    @Cacheable(value = "imageInfo", key = "#url")
    public Result<ImageInfoVO> getImageInfo(@RequestParam String url) {
        try {
            if (!StringUtils.hasText(url)) {
                return Result.failure("图片URL不能为空");
            }

            // 解析图片路径
            String imagePath = extractImagePath(url);
            Path fullPath = Paths.get(uploadPath + imagePath);

            if (!Files.exists(fullPath)) {
                return Result.failure("图片文件不存在");
            }

            // 获取文件信息
            long fileSize = Files.size(fullPath);
            String contentType = Files.probeContentType(fullPath);
            
            // 构建图片信息
            ImageInfoVO imageInfo = new ImageInfoVO();
            imageInfo.setUrl(url);
            imageInfo.setPath(imagePath);
            imageInfo.setSize(fileSize);
            imageInfo.setContentType(contentType);
            imageInfo.setLastModified(Files.getLastModifiedTime(fullPath).toMillis());

            // 生成不同尺寸的URL
            Map<String, String> variants = generateImageVariants(imagePath);
            imageInfo.setVariants(variants);

            return Result.success(imageInfo);

        } catch (Exception e) {
            log.error("获取图片信息时发生错误: {}", e.getMessage(), e);
            return Result.failure("获取图片信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取优化后的图片URL
     * 
     * @param urls 原始图片URL列表
     * @param size 目标尺寸
     * @param request HTTP请求对象
     * @return 优化后的图片URL映射
     */
    @PostMapping("/batch-optimize")
    public Result<Map<String, String>> batchOptimizeImages(
            @RequestBody java.util.List<String> urls,
            @RequestParam(defaultValue = "medium") String size,
            HttpServletRequest request) {

        try {
            if (urls == null || urls.isEmpty()) {
                return Result.failure("图片URL列表不能为空");
            }

            String userAgent = request.getHeader("User-Agent");
            String networkType = request.getHeader("X-Network-Type");
            String deviceType = detectDeviceType(userAgent);

            Map<String, String> optimizedUrls = new HashMap<>();

            for (String url : urls) {
                if (StringUtils.hasText(url)) {
                    String imagePath = extractImagePath(url);
                    String optimizedUrl = imageCompressionService.smartCompress(
                        imagePath, size, deviceType, networkType);
                    optimizedUrls.put(url, optimizedUrl);
                }
            }

            return Result.success(optimizedUrls);

        } catch (Exception e) {
            log.error("批量优化图片时发生错误: {}", e.getMessage(), e);
            return Result.failure("批量优化图片失败: " + e.getMessage());
        }
    }

    /**
     * 预热图片缓存
     * 
     * @param urls 需要预热的图片URL列表
     * @param sizes 需要预热的尺寸列表
     * @return 预热结果
     */
    @PostMapping("/preheat")
    public Result<Map<String, Object>> preheatImages(
            @RequestBody java.util.List<String> urls,
            @RequestParam(defaultValue = "thumb,medium") String sizes) {

        try {
            if (!cacheEnabled) {
                return Result.failure("缓存功能未启用");
            }

            String[] sizeArray = sizes.split(",");
            int totalCount = urls.size() * sizeArray.length;
            int successCount = 0;

            for (String url : urls) {
                for (String size : sizeArray) {
                    try {
                        String imagePath = extractImagePath(url);
                        String optimizedUrl = imageCompressionService.compressImage(imagePath, size.trim());
                        
                        // 预加载图片到缓存
                        preloadImageToCache(optimizedUrl);
                        successCount++;
                        
                    } catch (Exception e) {
                        log.warn("预热图片失败: {} ({})", url, size, e);
                    }
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failureCount", totalCount - successCount);
            result.put("successRate", (double) successCount / totalCount * 100);

            return Result.success(result);

        } catch (Exception e) {
            log.error("预热图片缓存时发生错误: {}", e.getMessage(), e);
            return Result.failure("预热图片缓存失败: " + e.getMessage());
        }
    }

    /**
     * 清理图片缓存
     * 
     * @param pattern 缓存键模式 (可选)
     * @return 清理结果
     */
    @DeleteMapping("/cache")
    public Result<String> clearImageCache(@RequestParam(required = false) String pattern) {
        try {
            // 这里应该调用缓存服务清理缓存
            // cacheService.clearCache(pattern);
            
            log.info("清理图片缓存: {}", pattern != null ? pattern : "全部");
            return Result.success("缓存清理成功");

        } catch (Exception e) {
            log.error("清理图片缓存时发生错误: {}", e.getMessage(), e);
            return Result.failure("清理图片缓存失败: " + e.getMessage());
        }
    }

    /**
     * 检测设备类型
     */
    private String detectDeviceType(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return "mobile";
        }

        userAgent = userAgent.toLowerCase();
        
        if (userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone")) {
            return "mobile";
        } else if (userAgent.contains("tablet") || userAgent.contains("ipad")) {
            return "tablet";
        } else {
            return "desktop";
        }
    }

    /**
     * 获取媒体类型
     */
    private MediaType getMediaType(String path) {
        String extension = path.substring(path.lastIndexOf(".") + 1).toLowerCase();
        
        switch (extension) {
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "png":
                return MediaType.IMAGE_PNG;
            case "gif":
                return MediaType.IMAGE_GIF;
            case "webp":
                return MediaType.valueOf("image/webp");
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }

    /**
     * 尝试获取WebP版本
     */
    private String tryGetWebPVersion(String originalUrl) {
        // 如果服务器支持WebP转换，在这里实现
        // 目前返回原始URL
        return originalUrl;
    }

    /**
     * 从URL中提取图片路径
     */
    private String extractImagePath(String url) {
        if (url.startsWith("http")) {
            // 从完整URL中提取路径部分
            int index = url.indexOf("/weshop-wjhx/uploads");
            if (index != -1) {
                return url.substring(index);
            }
        }
        return url;
    }

    /**
     * 生成图片的不同尺寸变体
     */
    private Map<String, String> generateImageVariants(String imagePath) {
        Map<String, String> variants = new HashMap<>();
        
        String[] sizes = {"thumb", "medium", "large", "avatar", "banner", "comment"};
        for (String size : sizes) {
            String variantUrl = imageCompressionService.compressImage(imagePath, size);
            variants.put(size, variantUrl);
        }
        
        return variants;
    }

    /**
     * 预加载图片到缓存
     */
    private void preloadImageToCache(String imageUrl) {
        // 这里实现图片预加载逻辑
        // 可以使用HTTP客户端请求图片，触发缓存
        log.debug("预加载图片到缓存: {}", imageUrl);
    }
}