package com.logic.code.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 缓存配置类
 * 配置Spring Cache的相关组件
 * 
 * <AUTHOR>
 * @date 2025/8/4
 */
@Configuration
public class CacheConfig implements CachingConfigurer {

    /**
     * 缓存管理器
     * 使用ConcurrentMapCacheManager作为默认的缓存管理器
     */
    @Bean
    @Primary
    @Override
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 预定义缓存名称
        cacheManager.setCacheNames(
            "entityClass",      // 实体类缓存
            "imageUrls",        // 图片URL缓存（已存在）
            "imageInfo",        // 图片信息缓存（已存在）
            "methodCache",      // 方法级缓存
            "reflectionCache"   // 反射相关缓存
        );
        
        // 允许动态创建缓存
        cacheManager.setAllowNullValues(false);
        
        return cacheManager;
    }

    /**
     * 缓存键生成器
     * 为缓存生成唯一的键
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName());
            sb.append(".");
            sb.append(method.getName());
            
            if (params.length > 0) {
                sb.append("(");
                for (int i = 0; i < params.length; i++) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    if (params[i] != null) {
                        sb.append(params[i].toString());
                    } else {
                        sb.append("null");
                    }
                }
                sb.append(")");
            }
            
            return sb.toString();
        };
    }

    /**
     * 缓存错误处理器
     * 当缓存操作出现异常时的处理策略
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new SimpleCacheErrorHandler();
    }

    /**
     * 缓存解析器
     * 可以根据不同的条件选择不同的缓存
     */
    @Override
    public CacheResolver cacheResolver() {
        return null; // 使用默认的缓存解析器
    }
}
