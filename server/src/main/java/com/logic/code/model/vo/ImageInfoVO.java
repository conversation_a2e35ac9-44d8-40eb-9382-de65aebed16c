package com.logic.code.model.vo;

import lombok.Data;

import java.util.Map;

/**
 * 图片信息VO
 * 
 * <AUTHOR>
 */
@Data
public class ImageInfoVO {
    
    /**
     * 图片URL
     */
    private String url;
    
    /**
     * 图片路径
     */
    private String path;
    
    /**
     * 文件大小（字节）
     */
    private Long size;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 最后修改时间
     */
    private Long lastModified;
    
    /**
     * 图片宽度
     */
    private Integer width;
    
    /**
     * 图片高度
     */
    private Integer height;
    
    /**
     * 不同尺寸的变体URL
     * key: 尺寸类型 (thumb, medium, large等)
     * value: 对应的图片URL
     */
    private Map<String, String> variants;
    
    /**
     * 是否支持WebP格式
     */
    private Boolean supportsWebP;
    
    /**
     * 压缩率（相对于原图）
     */
    private Double compressionRatio;
    
    /**
     * 缓存状态
     */
    private String cacheStatus;
}