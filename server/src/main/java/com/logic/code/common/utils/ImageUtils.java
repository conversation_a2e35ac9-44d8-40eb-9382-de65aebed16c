package com.logic.code.common.utils;

import com.logic.code.service.ImageCompressionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 图片处理工具类
 * 用于自动处理VO对象中的图片URL压缩
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImageUtils {

    @Autowired
    private ImageCompressionService imageCompressionService;

    // 缓存反射信息，提高性能
    private static final Map<Class<?>, List<Field>> IMAGE_FIELDS_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Method> SETTER_CACHE = new ConcurrentHashMap<>();

    /**
     * 自动处理对象中的图片字段
     * 
     * @param obj 要处理的对象
     * @param sizeType 图片尺寸类型
     * @param <T> 对象类型
     * @return 处理后的对象
     */
    public <T> T processImageFields(T obj, String sizeType) {
        if (obj == null) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();
            List<Field> imageFields = getImageFields(clazz);

            for (Field field : imageFields) {
                processImageField(obj, field, sizeType);
            }
        } catch (Exception e) {
            log.error("处理图片字段时发生错误: {}", e.getMessage(), e);
        }

        return obj;
    }

    /**
     * 批量处理对象列表中的图片字段
     * 
     * @param list 对象列表
     * @param sizeType 图片尺寸类型
     * @param <T> 对象类型
     * @return 处理后的对象列表
     */
    public <T> List<T> processImageFieldsBatch(List<T> list, String sizeType) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        for (T obj : list) {
            processImageFields(obj, sizeType);
        }

        return list;
    }

    /**
     * 根据请求头信息智能处理图片字段
     * 
     * @param obj 要处理的对象
     * @param sizeType 图片尺寸类型
     * @param userAgent 用户代理字符串
     * @param networkType 网络类型
     * @param <T> 对象类型
     * @return 处理后的对象
     */
    public <T> T smartProcessImageFields(T obj, String sizeType, String userAgent, String networkType) {
        if (obj == null) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();
            List<Field> imageFields = getImageFields(clazz);
            String deviceType = detectDeviceType(userAgent);

            for (Field field : imageFields) {
                processImageFieldSmart(obj, field, sizeType, deviceType, networkType);
            }
        } catch (Exception e) {
            log.error("智能处理图片字段时发生错误: {}", e.getMessage(), e);
        }

        return obj;
    }

    /**
     * 获取类中的图片字段
     */
    private List<Field> getImageFields(Class<?> clazz) {
        return IMAGE_FIELDS_CACHE.computeIfAbsent(clazz, k -> {
            java.util.List<Field> imageFields = new java.util.ArrayList<>();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                if (isImageField(field)) {
                    field.setAccessible(true);
                    imageFields.add(field);
                }
            }

            return imageFields;
        });
    }

    /**
     * 判断字段是否为图片字段
     */
    private boolean isImageField(Field field) {
        String fieldName = field.getName().toLowerCase();
        return fieldName.contains("pic") || 
               fieldName.contains("image") || 
               fieldName.contains("img") || 
               fieldName.contains("photo") || 
               fieldName.contains("avatar") ||
               fieldName.contains("banner") ||
               fieldName.endsWith("url") && (
                   fieldName.contains("pic") || 
                   fieldName.contains("img") || 
                   fieldName.contains("photo")
               );
    }

    /**
     * 处理单个图片字段
     */
    private void processImageField(Object obj, Field field, String sizeType) {
        try {
            Object value = field.get(obj);
            if (value instanceof String) {
                String originalUrl = (String) value;
                if (StringUtils.hasText(originalUrl)) {
                    String compressedUrl = imageCompressionService.compressImage(originalUrl, sizeType);
                    field.set(obj, compressedUrl);
                }
            }
        } catch (Exception e) {
            log.warn("处理图片字段 {} 时发生错误: {}", field.getName(), e.getMessage());
        }
    }

    /**
     * 智能处理单个图片字段
     */
    private void processImageFieldSmart(Object obj, Field field, String sizeType, String deviceType, String networkType) {
        try {
            Object value = field.get(obj);
            if (value instanceof String) {
                String originalUrl = (String) value;
                if (StringUtils.hasText(originalUrl)) {
                    String compressedUrl = imageCompressionService.smartCompress(originalUrl, sizeType, deviceType, networkType);
                    field.set(obj, compressedUrl);
                }
            }
        } catch (Exception e) {
            log.warn("智能处理图片字段 {} 时发生错误: {}", field.getName(), e.getMessage());
        }
    }

    /**
     * 根据User-Agent检测设备类型
     */
    private String detectDeviceType(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return "mobile"; // 默认移动设备
        }

        userAgent = userAgent.toLowerCase();
        
        if (userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone")) {
            return "mobile";
        } else if (userAgent.contains("tablet") || userAgent.contains("ipad")) {
            return "tablet";
        } else {
            return "desktop";
        }
    }

    /**
     * 手动压缩单个图片URL
     * 
     * @param originalUrl 原始URL
     * @param sizeType 尺寸类型
     * @return 压缩后的URL
     */
    public String compressImageUrl(String originalUrl, String sizeType) {
        return imageCompressionService.compressImage(originalUrl, sizeType);
    }

    /**
     * 手动压缩单个图片URL（带自定义质量）
     * 
     * @param originalUrl 原始URL
     * @param sizeType 尺寸类型
     * @param quality 图片质量
     * @return 压缩后的URL
     */
    public String compressImageUrl(String originalUrl, String sizeType, Integer quality) {
        return imageCompressionService.compressImage(originalUrl, sizeType, quality);
    }

    /**
     * 批量压缩图片URL
     * 
     * @param originalUrls 原始URL列表
     * @param sizeType 尺寸类型
     * @return 压缩后的URL映射
     */
    public Map<String, String> batchCompressImageUrls(List<String> originalUrls, String sizeType) {
        return imageCompressionService.batchCompress(originalUrls, sizeType);
    }
}