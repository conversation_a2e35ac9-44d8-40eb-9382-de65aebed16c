package com.logic.code.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 图片压缩服务
 * 统一处理图片URL的压缩和优化
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageCompressionService {

    @Value("${app.image.base-url:https://www.sxwjsm.com}")
    private String baseUrl;

    @Value("${app.image.compression.enabled:true}")
    private boolean compressionEnabled;

    /**
     * 图片尺寸配置
     */
    private static final Map<String, ImageSize> IMAGE_SIZES = new HashMap<>();

    static {
        // 缩略图 - 商品列表、搜索结果
        IMAGE_SIZES.put("thumb", new ImageSize(200, 200, 75));
        // 中等尺寸 - 商品详情页
        IMAGE_SIZES.put("medium", new ImageSize(400, 400, 80));
        // 大尺寸 - 轮播图、详情大图
        IMAGE_SIZES.put("large", new ImageSize(750, 400, 85));
        // 头像 - 用户头像
        IMAGE_SIZES.put("avatar", new ImageSize(100, 100, 70));
        // 横幅 - 活动横幅
        IMAGE_SIZES.put("banner", new ImageSize(750, 300, 85));
        // 评论图片 - 用户评论上传
        IMAGE_SIZES.put("comment", new ImageSize(300, 300, 75));
        // 商品详情页图片
        IMAGE_SIZES.put("detail", new ImageSize(750, 750, 85));
    }

    /**
     * 压缩图片URL
     * 
     * @param originalUrl 原始图片URL
     * @param sizeType 尺寸类型 (thumb, medium, large, avatar, banner, comment, detail)
     * @param customQuality 自定义质量 (可选，1-100)
     * @return 压缩后的图片URL
     */
    public String compressImage(String originalUrl, String sizeType, Integer customQuality) {
        // 如果URL为空或压缩功能未启用，返回原URL
        if (!StringUtils.hasText(originalUrl) || !compressionEnabled) {
            return originalUrl;
        }

        // 如果已经是完整URL或已经包含压缩参数，直接返回
        if (originalUrl.startsWith("http://") || originalUrl.startsWith("https://") || originalUrl.contains("imageView2")) {
            return originalUrl;
        }

        // 只处理项目内的图片
        if (!originalUrl.startsWith("/weshop-wjhx/uploads")) {
            return originalUrl;
        }

        // 获取尺寸配置
        ImageSize imageSize = IMAGE_SIZES.get(sizeType);
        if (imageSize == null) {
            imageSize = IMAGE_SIZES.get("medium"); // 默认使用中等尺寸
        }

        // 使用自定义质量或默认质量
        int quality = customQuality != null ? customQuality : imageSize.quality;
        quality = Math.max(1, Math.min(100, quality)); // 确保质量在1-100之间

        // 构建压缩URL
        String compressedUrl = String.format("%s%s?imageView2/2/w/%d/h/%d/q/%d",
                baseUrl, originalUrl, imageSize.width, imageSize.height, quality);

        log.debug("图片压缩: {} -> {}", originalUrl, compressedUrl);
        return compressedUrl;
    }

    /**
     * 压缩图片URL (使用默认质量)
     * 
     * @param originalUrl 原始图片URL
     * @param sizeType 尺寸类型
     * @return 压缩后的图片URL
     */
    public String compressImage(String originalUrl, String sizeType) {
        return compressImage(originalUrl, sizeType, null);
    }

    /**
     * 根据设备类型和网络状况智能压缩
     * 
     * @param originalUrl 原始图片URL
     * @param sizeType 尺寸类型
     * @param deviceType 设备类型 (mobile, tablet, desktop)
     * @param networkType 网络类型 (wifi, 4g, 3g, 2g)
     * @return 压缩后的图片URL
     */
    public String smartCompress(String originalUrl, String sizeType, String deviceType, String networkType) {
        // 如果URL为空或压缩功能未启用，返回原URL
        if (!StringUtils.hasText(originalUrl) || !compressionEnabled) {
            return originalUrl;
        }

        // 如果已经是完整URL或已经包含压缩参数，直接返回
        if (originalUrl.startsWith("http://") || originalUrl.startsWith("https://") || originalUrl.contains("imageView2")) {
            return originalUrl;
        }

        // 只处理项目内的图片
        if (!originalUrl.startsWith("/weshop-wjhx/uploads")) {
            return originalUrl;
        }

        ImageSize baseSize = IMAGE_SIZES.get(sizeType);
        if (baseSize == null) {
            baseSize = IMAGE_SIZES.get("medium");
        }

        // 根据网络状况调整质量
        int quality = adjustQualityByNetwork(baseSize.quality, networkType);
        
        // 根据设备类型调整尺寸
        ImageSize adjustedSize = adjustSizeByDevice(baseSize, deviceType);

        // 构建智能压缩URL
        String compressedUrl = String.format("%s%s?imageView2/2/w/%d/h/%d/q/%d",
                baseUrl, originalUrl, adjustedSize.width, adjustedSize.height, quality);

        log.debug("智能压缩: {} -> {} (设备: {}, 网络: {})", originalUrl, compressedUrl, deviceType, networkType);
        return compressedUrl;
    }

    /**
     * 根据网络状况调整图片质量
     */
    private int adjustQualityByNetwork(int baseQuality, String networkType) {
        if (networkType == null) {
            return baseQuality;
        }

        switch (networkType.toLowerCase()) {
            case "wifi":
                return Math.min(baseQuality + 10, 95); // WiFi环境提高质量
            case "4g":
                return baseQuality; // 4G保持原质量
            case "3g":
                return Math.max(baseQuality - 10, 60); // 3G降低质量
            case "2g":
                return Math.max(baseQuality - 20, 50); // 2G大幅降低质量
            default:
                return baseQuality;
        }
    }

    /**
     * 根据设备类型调整图片尺寸
     */
    private ImageSize adjustSizeByDevice(ImageSize baseSize, String deviceType) {
        if (deviceType == null) {
            return baseSize;
        }

        switch (deviceType.toLowerCase()) {
            case "mobile":
                // 移动设备使用较小尺寸
                return new ImageSize(
                    Math.min(baseSize.width, 750),
                    Math.min(baseSize.height, 750),
                    baseSize.quality
                );
            case "tablet":
                // 平板设备使用中等尺寸
                return new ImageSize(
                    Math.min(baseSize.width * 4 / 3, 1000),
                    Math.min(baseSize.height * 4 / 3, 1000),
                    baseSize.quality
                );
            case "desktop":
                // 桌面设备使用较大尺寸
                return new ImageSize(
                    Math.min(baseSize.width * 3 / 2, 1200),
                    Math.min(baseSize.height * 3 / 2, 1200),
                    baseSize.quality
                );
            default:
                return baseSize;
        }
    }

    /**
     * 批量压缩图片URL
     * 
     * @param originalUrls 原始图片URL列表
     * @param sizeType 尺寸类型
     * @return 压缩后的图片URL列表
     */
    public Map<String, String> batchCompress(java.util.List<String> originalUrls, String sizeType) {
        Map<String, String> result = new HashMap<>();
        
        if (originalUrls == null || originalUrls.isEmpty()) {
            return result;
        }

        for (String url : originalUrls) {
            if (StringUtils.hasText(url)) {
                result.put(url, compressImage(url, sizeType));
            }
        }

        return result;
    }

    /**
     * 生成响应式图片URL集合
     * 
     * @param originalUrl 原始图片URL
     * @return 不同尺寸的图片URL映射
     */
    public Map<String, String> generateResponsiveImages(String originalUrl) {
        Map<String, String> responsiveImages = new HashMap<>();
        
        if (!StringUtils.hasText(originalUrl)) {
            return responsiveImages;
        }

        // 为所有支持的尺寸生成URL
        for (String sizeType : IMAGE_SIZES.keySet()) {
            responsiveImages.put(sizeType, compressImage(originalUrl, sizeType));
        }

        return responsiveImages;
    }

    /**
     * 根据网络状况和设备类型推荐最佳图片尺寸
     * 
     * @param deviceType 设备类型
     * @param networkType 网络类型
     * @param context 使用场景 (list, detail, avatar等)
     * @return 推荐的尺寸类型
     */
    public String recommendImageSize(String deviceType, String networkType, String context) {
        // 根据使用场景确定基础尺寸
        String baseSize = getBaseSizeByContext(context);
        
        // 根据设备类型调整
        baseSize = adjustSizeByDevice(baseSize, deviceType);
        
        // 根据网络状况进一步调整
        return adjustSizeByNetwork(baseSize, networkType);
    }

    /**
     * 检查图片是否需要压缩
     * 
     * @param originalUrl 原始图片URL
     * @param targetSize 目标尺寸
     * @return 是否需要压缩
     */
    public boolean needsCompression(String originalUrl, String targetSize) {
        if (!StringUtils.hasText(originalUrl) || !compressionEnabled) {
            return false;
        }

        // 如果已经是压缩后的URL，不需要再次压缩
        if (originalUrl.contains("imageView2")) {
            return false;
        }

        // 如果不是项目内图片，不需要压缩
        if (!originalUrl.startsWith("/weshop-wjhx/uploads")) {
            return false;
        }

        return true;
    }

    /**
     * 获取图片压缩统计信息
     * 
     * @return 压缩统计信息
     */
    public Map<String, Object> getCompressionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("enabled", compressionEnabled);
        stats.put("baseUrl", baseUrl);
        stats.put("supportedSizes", IMAGE_SIZES.keySet());
        stats.put("totalSizeConfigs", IMAGE_SIZES.size());
        
        return stats;
    }

    /**
     * 根据使用场景获取基础尺寸
     */
    private String getBaseSizeByContext(String context) {
        if (context == null) {
            return "medium";
        }

        switch (context.toLowerCase()) {
            case "list":
            case "search":
                return "thumb";
            case "detail":
            case "gallery":
                return "large";
            case "avatar":
            case "profile":
                return "avatar";
            case "banner":
            case "hero":
                return "banner";
            case "comment":
            case "review":
                return "comment";
            default:
                return "medium";
        }
    }

    /**
     * 根据设备类型调整尺寸
     */
    private String adjustSizeByDevice(String baseSize, String deviceType) {
        if (deviceType == null) {
            return baseSize;
        }

        // 移动设备倾向于使用较小尺寸
        if ("mobile".equals(deviceType.toLowerCase())) {
            switch (baseSize) {
                case "large":
                    return "medium";
                case "banner":
                    return "large";
                default:
                    return baseSize;
            }
        }

        // 桌面设备可以使用较大尺寸
        if ("desktop".equals(deviceType.toLowerCase())) {
            switch (baseSize) {
                case "thumb":
                    return "medium";
                case "medium":
                    return "large";
                default:
                    return baseSize;
            }
        }

        return baseSize;
    }

    /**
     * 根据网络状况调整尺寸
     */
    private String adjustSizeByNetwork(String baseSize, String networkType) {
        if (networkType == null) {
            return baseSize;
        }

        // 弱网环境使用较小尺寸
        if ("2g".equals(networkType.toLowerCase()) || "3g".equals(networkType.toLowerCase())) {
            switch (baseSize) {
                case "large":
                    return "medium";
                case "medium":
                    return "thumb";
                case "banner":
                    return "large";
                default:
                    return baseSize;
            }
        }

        return baseSize;
    }

    /**
     * 获取所有支持的尺寸类型
     */
    public Map<String, ImageSize> getSupportedSizes() {
        return new HashMap<>(IMAGE_SIZES);
    }

    /**
     * 图片尺寸配置类
     */
    public static class ImageSize {
        public final int width;
        public final int height;
        public final int quality;

        public ImageSize(int width, int height, int quality) {
            this.width = width;
            this.height = height;
            this.quality = quality;
        }

        @Override
        public String toString() {
            return String.format("ImageSize{width=%d, height=%d, quality=%d}", width, height, quality);
        }
    }
}