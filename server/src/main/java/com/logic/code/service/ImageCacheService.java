package com.logic.code.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 图片缓存服务
 * 提供图片缓存管理功能
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageCacheService {

    @Value("${app.image.cache.path:/cache/images}")
    private String cachePath;

    @Value("${app.image.cache.enabled:true}")
    private boolean cacheEnabled;

    @Value("${app.image.cache.max-size:1000}")
    private int maxCacheSize;

    @Value("${app.image.cache.ttl:3600}")
    private long cacheTtl; // 缓存过期时间（秒）

    // 内存缓存，存储图片URL映射
    private final Map<String, CacheEntry> memoryCache = new ConcurrentHashMap<>();

    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final String url;
        private final long timestamp;
        private final long accessCount;

        public CacheEntry(String url) {
            this.url = url;
            this.timestamp = System.currentTimeMillis();
            this.accessCount = 1;
        }

        public boolean isExpired(long ttl) {
            return System.currentTimeMillis() - timestamp > ttl * 1000;
        }
    }

    /**
     * 获取缓存的图片URL
     * 
     * @param originalUrl 原始图片URL
     * @param sizeType 尺寸类型
     * @return 缓存的图片URL，如果不存在则返回null
     */
    @Cacheable(value = "imageUrls", key = "#originalUrl + '_' + #sizeType")
    public String getCachedImageUrl(String originalUrl, String sizeType) {
        if (!cacheEnabled || !StringUtils.hasText(originalUrl)) {
            return null;
        }

        String cacheKey = generateCacheKey(originalUrl, sizeType);
        CacheEntry entry = memoryCache.get(cacheKey);

        if (entry != null && !entry.isExpired(cacheTtl)) {
            log.debug("从缓存获取图片URL: {} -> {}", originalUrl, entry.url);
            return entry.url;
        }

        // 缓存过期或不存在
        if (entry != null) {
            memoryCache.remove(cacheKey);
        }

        return null;
    }

    /**
     * 缓存图片URL
     * 
     * @param originalUrl 原始图片URL
     * @param sizeType 尺寸类型
     * @param compressedUrl 压缩后的图片URL
     */
    public void cacheImageUrl(String originalUrl, String sizeType, String compressedUrl) {
        if (!cacheEnabled || !StringUtils.hasText(originalUrl) || !StringUtils.hasText(compressedUrl)) {
            return;
        }

        String cacheKey = generateCacheKey(originalUrl, sizeType);
        
        // 检查缓存大小限制
        if (memoryCache.size() >= maxCacheSize) {
            cleanupExpiredEntries();
            
            // 如果清理后仍然超过限制，移除最旧的条目
            if (memoryCache.size() >= maxCacheSize) {
                removeOldestEntry();
            }
        }

        memoryCache.put(cacheKey, new CacheEntry(compressedUrl));
        log.debug("缓存图片URL: {} -> {}", originalUrl, compressedUrl);
    }

    /**
     * 检查本地文件缓存是否存在
     * 
     * @param imagePath 图片路径
     * @param sizeType 尺寸类型
     * @return 是否存在有效的本地缓存
     */
    public boolean isLocalCacheValid(String imagePath, String sizeType) {
        if (!cacheEnabled) {
            return false;
        }

        try {
            String cacheFileName = generateCacheFileName(imagePath, sizeType);
            Path cacheFilePath = Paths.get(cachePath, cacheFileName);
            
            if (!Files.exists(cacheFilePath)) {
                return false;
            }

            // 检查缓存文件是否过期
            long lastModified = Files.getLastModifiedTime(cacheFilePath).toMillis();
            long now = System.currentTimeMillis();
            
            return (now - lastModified) < (cacheTtl * 1000);

        } catch (IOException e) {
            log.warn("检查本地缓存时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建本地缓存文件
     * 
     * @param imagePath 图片路径
     * @param sizeType 尺寸类型
     * @param imageData 图片数据
     * @return 缓存文件路径
     */
    public String createLocalCache(String imagePath, String sizeType, byte[] imageData) {
        if (!cacheEnabled || imageData == null || imageData.length == 0) {
            return null;
        }

        try {
            // 确保缓存目录存在
            Path cacheDir = Paths.get(cachePath);
            if (!Files.exists(cacheDir)) {
                Files.createDirectories(cacheDir);
            }

            String cacheFileName = generateCacheFileName(imagePath, sizeType);
            Path cacheFilePath = cacheDir.resolve(cacheFileName);

            // 写入缓存文件
            Files.write(cacheFilePath, imageData);
            
            log.debug("创建本地缓存文件: {}", cacheFilePath);
            return cacheFilePath.toString();

        } catch (IOException e) {
            log.error("创建本地缓存文件时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清理过期的缓存条目
     */
    @CacheEvict(value = "imageUrls", allEntries = true)
    public void cleanupExpiredEntries() {
        if (!cacheEnabled) {
            return;
        }

        AtomicInteger removedCount = new AtomicInteger();
        long now = System.currentTimeMillis();

        memoryCache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired(cacheTtl);
            if (expired) {
                removedCount.getAndIncrement();
            }
            return expired;
        });

        if (removedCount.get() > 0) {
            log.info("清理过期缓存条目: {} 个", removedCount);
        }

        // 同时清理本地缓存文件
        cleanupLocalCacheFiles();
    }

    /**
     * 清理本地缓存文件
     */
    private void cleanupLocalCacheFiles() {
        try {
            Path cacheDir = Paths.get(cachePath);
            if (!Files.exists(cacheDir)) {
                return;
            }

            long now = System.currentTimeMillis();
            AtomicInteger removedCount = new AtomicInteger();

            Files.list(cacheDir)
                .filter(Files::isRegularFile)
                .forEach(file -> {
                    try {
                        long lastModified = Files.getLastModifiedTime(file).toMillis();
                        if ((now - lastModified) > (cacheTtl * 1000)) {
                            Files.delete(file);
                            removedCount.getAndIncrement();
                        }
                    } catch (IOException e) {
                        log.warn("删除过期缓存文件时发生错误: {}", e.getMessage());
                    }
                });

            if (removedCount.get() > 0) {
                log.info("清理过期本地缓存文件: {} 个", removedCount);
            }

        } catch (IOException e) {
            log.error("清理本地缓存文件时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 移除最旧的缓存条目
     */
    private void removeOldestEntry() {
        if (memoryCache.isEmpty()) {
            return;
        }

        String oldestKey = null;
        long oldestTimestamp = Long.MAX_VALUE;

        for (Map.Entry<String, CacheEntry> entry : memoryCache.entrySet()) {
            if (entry.getValue().timestamp < oldestTimestamp) {
                oldestTimestamp = entry.getValue().timestamp;
                oldestKey = entry.getKey();
            }
        }

        if (oldestKey != null) {
            memoryCache.remove(oldestKey);
            log.debug("移除最旧的缓存条目: {}", oldestKey);
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String originalUrl, String sizeType) {
        return originalUrl.hashCode() + "_" + sizeType;
    }

    /**
     * 生成缓存文件名
     */
    private String generateCacheFileName(String imagePath, String sizeType) {
        String fileName = imagePath.replaceAll("[/\\\\]", "_");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("%s_%s_%s.cache", fileName, sizeType, timestamp);
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("enabled", cacheEnabled);
        stats.put("memoryCacheSize", memoryCache.size());
        stats.put("maxCacheSize", maxCacheSize);
        stats.put("cacheTtl", cacheTtl);
        stats.put("cachePath", cachePath);

        // 统计本地缓存文件数量
        try {
            Path cacheDir = Paths.get(cachePath);
            if (Files.exists(cacheDir)) {
                long localCacheCount = Files.list(cacheDir)
                    .filter(Files::isRegularFile)
                    .count();
                stats.put("localCacheCount", localCacheCount);
            } else {
                stats.put("localCacheCount", 0);
            }
        } catch (IOException e) {
            stats.put("localCacheCount", -1);
        }

        return stats;
    }

    /**
     * 清空所有缓存
     */
    @CacheEvict(value = "imageUrls", allEntries = true)
    public void clearAllCache() {
        memoryCache.clear();
        
        // 清空本地缓存文件
        try {
            Path cacheDir = Paths.get(cachePath);
            if (Files.exists(cacheDir)) {
                Files.list(cacheDir)
                    .filter(Files::isRegularFile)
                    .forEach(file -> {
                        try {
                            Files.delete(file);
                        } catch (IOException e) {
                            log.warn("删除缓存文件时发生错误: {}", e.getMessage());
                        }
                    });
            }
        } catch (IOException e) {
            log.error("清空本地缓存时发生错误: {}", e.getMessage(), e);
        }

        log.info("已清空所有图片缓存");
    }
}