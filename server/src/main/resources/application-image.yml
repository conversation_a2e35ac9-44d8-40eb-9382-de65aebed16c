# 图片服务配置
app:
  image:
    # 基础URL配置
    base-url: https://www.sxwjsm.com
    
    # 压缩功能配置
    compression:
      enabled: true
      default-quality: 80
      webp-enabled: true
      
    # 缓存配置
    cache:
      enabled: true
      path: /cache/images
      max-size: 1000
      ttl: 3600  # 缓存过期时间（秒）
      
    # CDN配置
    cdn:
      enabled: false
      domain: https://cdn.example.com
      cache-control: public, max-age=86400
      
    # 上传配置
    upload:
      max-file-size: 5MB
      allowed-types: 
        - image/jpeg
        - image/png
        - image/gif
        - image/webp
      auto-generate-variants: true
      
    # 尺寸配置
    sizes:
      thumb:
        width: 200
        height: 200
        quality: 75
        description: "缩略图 - 商品列表"
      medium:
        width: 400
        height: 400
        quality: 80
        description: "中等尺寸 - 商品详情"
      large:
        width: 750
        height: 400
        quality: 85
        description: "大尺寸 - 轮播图"
      avatar:
        width: 100
        height: 100
        quality: 70
        description: "头像尺寸"
      banner:
        width: 750
        height: 300
        quality: 85
        description: "横幅尺寸"
      comment:
        width: 300
        height: 300
        quality: 75
        description: "评论图片"
      detail:
        width: 750
        height: 750
        quality: 85
        description: "商品详情页图片"
        
    # 网络自适应配置
    network-adaptive:
      enabled: true
      wifi-quality-boost: 10
      mobile-quality-reduction: 10
      slow-network-quality-reduction: 20
      
    # 设备自适应配置
    device-adaptive:
      enabled: true
      mobile-size-reduction: true
      tablet-size-boost: true
      desktop-size-boost: true
      
    # 监控配置
    monitoring:
      enabled: true
      log-compression-stats: true
      log-cache-stats: true